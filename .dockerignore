# 依赖
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# 构建产物
.next/
out/
dist/
build/

# 测试
coverage/
*.lcov

# 环境配置
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 版本控制
.git
.gitignore

# IDE/编辑器
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统
.DS_Store
Thumbs.db

# 日志
*.log
logs/

# 临时文件
tmp/
temp/

# Docker 相关
Dockerfile
.dockerignore
docker-compose*.yml

# 文档
README.md
*.md 