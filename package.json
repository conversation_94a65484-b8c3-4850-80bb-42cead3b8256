{"name": "le-agent", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build --no-lint", "start": "next start", "lint": "next lint", "format": "prettier --write .", "format:check": "prettier --check .", "format:tailwind": "prettier --write '**/*.{js,jsx,ts,tsx,css,html}' --plugin=prettier-plugin-tailwindcss"}, "dependencies": {"@ai-sdk/deepseek": "^0.2.15", "@ai-sdk/openai": "^1.3.23", "@ai-sdk/provider": "^1.1.3", "@ai-sdk/provider-utils": "^2.2.8", "@ai-sdk/react": "^1.2.12", "@ai-sdk/xai": "^1.2.17", "@modelcontextprotocol/sdk": "^1.15.1", "@openrouter/ai-sdk-provider": "1.0.0-beta.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.7", "@tiptap/extension-mention": "^2.26.0", "@tiptap/react": "^2.26.0", "@tiptap/starter-kit": "^2.26.0", "@uidotdev/usehooks": "^2.4.1", "ai": "^4.3.17", "chokidar": "^4.0.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "consola": "^3.4.2", "deepmerge": "^4.3.1", "framer-motion": "^12.23.3", "hast-util-to-jsx-runtime": "^2.3.6", "lucide-react": "^0.525.0", "mermaid": "^11.8.1", "next": "15.3.5", "next-intl": "^4.3.4", "next-themes": "^0.4.6", "ollama-ai-provider": "^1.2.0", "qwen-ai-provider": "^0.1.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "remark-gfm": "^4.0.1", "sonner": "^2.0.6", "swr": "^2.3.4", "tailwind-merge": "^3.3.1", "ts-safe": "^0.0.5", "vaul": "^1.1.2", "zod": "^4.0.5", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "postcss": "^8.5.3", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "shiki": "^3.7.0", "tailwindcss": "^4", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.3.5", "typescript": "^5"}, "packageManager": "pnpm@8.15.1+sha1.8adba2d20330c02d3856e18c4eb3819d1d3ca6aa"}