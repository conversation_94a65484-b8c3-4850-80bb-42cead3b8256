'use client'

import { Button } from '@/components/ui/button'
import { useSpeechSynthesis } from '@/hooks/use-speech-synthesis'
import { useState } from 'react'

export default function SpeechTest() {
  const { isSupported, voices, speak, isPlaying } = useSpeechSynthesis()
  const [testResult, setTestResult] = useState<string>('')

  const runBasicTest = async () => {
    setTestResult('开始测试...')

    try {
      // 基础支持检查
      if (!isSupported) {
        setTestResult('❌ 浏览器不支持语音合成')
        return
      }

      // 检查语音引擎
      if (voices.length === 0) {
        setTestResult('⚠️ 没有可用的语音引擎，但会尝试使用默认语音')
      }

      // 尝试简单的语音合成
      await speak('测试', { volume: 0.5 })
      setTestResult('✅ 语音合成测试成功！')
    } catch (error) {
      setTestResult(`❌ 测试失败: ${error}`)
    }
  }

  const testSystemInfo = () => {
    const info = {
      userAgent: navigator.userAgent,
      speechSynthesis: !!window.speechSynthesis,
      voicesCount: voices.length,
      platform: navigator.platform,
      language: navigator.language
    }

    setTestResult(`系统信息:\n${JSON.stringify(info, null, 2)}`)
  }

  const testDirectAPI = () => {
    try {
      const utterance = new SpeechSynthesisUtterance('直接API测试')
      utterance.volume = 0.5
      utterance.rate = 1
      utterance.pitch = 1

      utterance.onstart = () => setTestResult('✅ 直接API测试开始播放')
      utterance.onend = () => setTestResult('✅ 直接API测试完成')
      utterance.onerror = e => setTestResult(`❌ 直接API测试失败: ${e.error}`)

      speechSynthesis.speak(utterance)
      setTestResult('🔄 直接API测试启动中...')
    } catch (error) {
      setTestResult(`❌ 直接API测试异常: ${error}`)
    }
  }

  return (
    <div className="mx-auto w-full max-w-2xl rounded-lg border p-6">
      <h2 className="mb-4 text-xl font-bold">语音合成诊断工具</h2>
      <div className="space-y-4">
        <div className="grid grid-cols-1 gap-2 md:grid-cols-3">
          <Button onClick={runBasicTest} disabled={isPlaying}>
            基础测试
          </Button>
          <Button onClick={testSystemInfo} variant="outline">
            系统信息
          </Button>
          <Button onClick={testDirectAPI} variant="outline">
            直接API测试
          </Button>
        </div>

        <div className="space-y-2">
          <h4 className="font-medium">浏览器兼容性:</h4>
          <div className="space-y-1 text-sm">
            <div>语音合成支持: {isSupported ? '✅' : '❌'}</div>
            <div>可用语音数量: {voices.length}</div>
            <div>当前状态: {isPlaying ? '播放中' : '待机'}</div>
          </div>
        </div>

        {testResult && (
          <div className="mt-4 rounded-md bg-gray-100 p-3">
            <h4 className="mb-2 font-medium">测试结果:</h4>
            <pre className="text-sm whitespace-pre-wrap">{testResult}</pre>
          </div>
        )}

        <div className="mt-4 rounded-md bg-blue-50 p-3 text-sm">
          <h4 className="mb-2 font-medium">故障排除提示:</h4>
          <ul className="list-inside list-disc space-y-1">
            <li>确保浏览器标签页没有被静音</li>
            <li>检查系统音量设置</li>
            <li>在Chrome中，需要用户交互后才能播放音频</li>
            <li>某些语音引擎需要网络连接</li>
            <li>尝试刷新页面重新加载语音引擎</li>
          </ul>
        </div>
      </div>
    </div>
  )
}
